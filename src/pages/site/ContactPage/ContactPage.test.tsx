import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { beforeEach,describe, expect, it, vi } from 'vitest';

import ContactPage from './index';

// Mock the dependencies
vi.mock('@/context/TenantContext', () => ({
  useTenant: () => ({
    activeAgent: 'regis',
    setActiveAgent: vi.fn(),
  }),
}));

vi.mock('@/hooks/useNotifications', () => ({
  useNotifications: () => ({
    notify: vi.fn(),
    notifications: [],
    dismiss: vi.fn(),
  }),
}));

vi.mock('@/services/scyraChatService', () => ({
  useScyraChatApi: () => vi.fn(),
}));

vi.mock('@/services/upivotalAgenticService', () => ({
  submitDemoRequest: vi.fn(),
}));

vi.mock('react-google-recaptcha', () => ({
  __esModule: true,
  default: vi.fn(({ onChange, onExpired, onErrored }) => (
    <div data-testid="recaptcha-mock">
      <button
        data-testid="recaptcha-verify"
        onClick={() => onChange('mock-token')}
      >
        Verify
      </button>
      <button
        data-testid="recaptcha-expire"
        onClick={() => onExpired()}
      >
        Expire
      </button>
      <button
        data-testid="recaptcha-error"
        onClick={() => onErrored()}
      >
        Error
      </button>
    </div>
  )),
}));

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('ContactPage reCAPTCHA Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the reCAPTCHA component', () => {
    renderWithProviders(<ContactPage />);
    expect(screen.getByTestId('recaptcha-mock')).toBeInTheDocument();
  });

  it('shows validation error when form is submitted without reCAPTCHA', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ContactPage />);

    // Fill required fields
    await user.type(screen.getByLabelText(/first name/i), 'John');
    await user.type(screen.getByLabelText(/last name/i), 'Doe');
    await user.type(screen.getByLabelText(/work email/i), '<EMAIL>');
    
    // Select a service
    const serviceSelect = screen.getByText('Select a service');
    await user.click(serviceSelect);
    const serviceOption = screen.getByText('SetIQ - Collection Services AI Agents Suite');
    await user.click(serviceOption);

    // Try to submit without reCAPTCHA
    const submitButton = screen.getByRole('button', { name: /submit/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Please complete the reCAPTCHA verification')).toBeInTheDocument();
    });
  });

  it('enables submit button when reCAPTCHA is verified', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ContactPage />);

    const submitButton = screen.getByRole('button', { name: /submit/i });
    expect(submitButton).toBeDisabled();

    // Verify reCAPTCHA
    const verifyButton = screen.getByTestId('recaptcha-verify');
    await user.click(verifyButton);

    await waitFor(() => {
      expect(submitButton).not.toBeDisabled();
    });
  });

  it('handles reCAPTCHA expiration', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ContactPage />);

    // First verify reCAPTCHA
    const verifyButton = screen.getByTestId('recaptcha-verify');
    await user.click(verifyButton);

    // Then expire it
    const expireButton = screen.getByTestId('recaptcha-expire');
    await user.click(expireButton);

    await waitFor(() => {
      expect(screen.getByText('reCAPTCHA has expired. Please verify again.')).toBeInTheDocument();
    });
  });

  it('handles reCAPTCHA error', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ContactPage />);

    const errorButton = screen.getByTestId('recaptcha-error');
    await user.click(errorButton);

    await waitFor(() => {
      expect(screen.getByText('reCAPTCHA verification failed. Please try again.')).toBeInTheDocument();
    });
  });
});
